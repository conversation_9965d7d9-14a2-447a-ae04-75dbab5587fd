import 'package:my_video/app_imports.dart';

class GuestHomePage extends StatefulWidget {
  const GuestHomePage({super.key});

  @override
  State<GuestHomePage> createState() => _GuestHomePageState();
}

class _GuestHomePageState extends State<GuestHomePage> {
  late HomeController _homeController;
  late ContentTabController _contentTabController;
  GuestHomePageHelper? _guestHomePageHelper;
  final ScrollController _scrollController = ScrollController();

  // Auto-scroll variables for slider
  Timer? _autoScrollTimer;
  PageController? _pageController;
  int _currentSliderPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _setupScrollListener();
    _startAutoScroll();
  }

  @override
  void dispose() {
    _stopAutoScroll();
    _pageController?.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      // Load more when user scrolls to 80% of the content
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent * 0.8) {
        _guestHomePageHelper?.loadMoreMovies();
      }
    });
  }

  void _startAutoScroll() {
    _autoScrollTimer = Timer.periodic(const Duration(seconds: 4), (timer) {
      final helper = _guestHomePageHelper;
      if (helper?.hasSliderMovies == true &&
          _pageController?.hasClients == true) {
        final nextPage = (_currentSliderPage + 1) % helper!.sliderMovies.length;
        _pageController?.animateToPage(
          nextPage,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  void _stopAutoScroll() {
    _autoScrollTimer?.cancel();
  }

  void _restartAutoScroll() {
    _stopAutoScroll();
    _startAutoScroll();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColorConstants.backgroundColor,
      body: GetBuilder<HomeController>(
        init: Get.put(HomeController(), permanent: true),
        builder: (HomeController controller) {
          _homeController = controller;
          // Initialize helper only after controller is available
          _guestHomePageHelper ??= GuestHomePageHelper(_homeController);
          // Initialize content tab controller
          _contentTabController = Get.put(ContentTabController());
          return _bodyView();
        },
      ),
    );
  }

  Widget _bodyView() {
    final helper = _guestHomePageHelper;
    if (helper == null) {
      return SafeArea(
        bottom: false,
        child: ShimmerWidgets.buildGuestHomePageShimmer(),
      );
    }

    return helper.isLoading
        ? SafeArea(
            bottom: false,
            child: ShimmerWidgets.buildGuestHomePageShimmer(),
          )
        : SafeArea(
            bottom: false,
            child: RefreshIndicator(
              onRefresh: helper.refreshData,
              child: CustomScrollView(
                controller: _scrollController,
                physics: const AlwaysScrollableScrollPhysics(),
                slivers: [
                  // App Bar
                  SliverToBoxAdapter(child: _appBar()),

                  // Tab Bar
                  SliverToBoxAdapter(child: _buildTabBar()),

                  // Slider (if available)
                  if (helper.hasSliderMovies)
                    SliverToBoxAdapter(child: _buildSliderSection()),

                  // Grid View
                  SliverToBoxAdapter(child: _buildGridSection()),

                  // Load More Indicator
                  if (helper.isLoadingMore)
                    SliverToBoxAdapter(child: _buildLoadMoreIndicator()),

                  // Bottom padding
                  SliverToBoxAdapter(child: Space.height(120)),
                ],
              ),
            ),
          );
  }

  Widget _appBar() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.width(16),
        vertical: MySize.height(12),
      ),
      child: Row(
        children: [
          // App Logo/Title
          Expanded(
            child: AppText(
              text: 'My Video',
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColorConstants.primaryColor,
            ),
          ),

          // Filter Button
          IconButton(
            onPressed: _showFilterBottomSheet,
            icon: Icon(
              Icons.filter_list,
              color: AppColorConstants.primaryColor,
              size: MySize.width(24),
            ),
          ),

          Space.width(8),

          // Login Button
          SizedBox(
            width: MySize.width(80),
            child: AppButton(
              text: 'Login',
              onPressed: () => gotoLoginPage(),
              backgroundColor: AppColorConstants.primaryColor,
              textColor: AppColorConstants.colorWhite,
              borderRadius: MySize.radius(8),
              padding: EdgeInsets.symmetric(
                horizontal: MySize.width(16),
                vertical: MySize.height(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return GetBuilder<ContentTabController>(
      builder: (tabController) => Container(
        margin: EdgeInsets.symmetric(horizontal: MySize.width(16)),
        decoration: BoxDecoration(
          color: AppColorConstants.cardColor,
          borderRadius: BorderRadius.circular(MySize.width(8)),
        ),
        child: TabBar(
          controller: tabController.tabController,
          onTap: (index) {
            final contentType = MovieContentType.values[index];
            final isMovie = contentType.apiValue;
            _guestHomePageHelper?.loadDataForContentType(isMovie);
          },
          indicator: BoxDecoration(
            color: AppColorConstants.primaryColor,
            borderRadius: BorderRadius.circular(MySize.width(8)),
          ),
          indicatorSize: TabBarIndicatorSize.tab,
          dividerColor: Colors.transparent,
          labelColor: AppColorConstants.colorWhite,
          unselectedLabelColor: AppColorConstants.colorGrey,
          labelStyle: TextStyle(
            fontSize: MySize.width(14),
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: TextStyle(
            fontSize: MySize.width(14),
            fontWeight: FontWeight.normal,
          ),
          tabs: tabController.allContentTypes
              .map((contentType) => Tab(text: contentType.displayName))
              .toList(),
        ),
      ),
    );
  }

  Widget _buildSliderSection() {
    final helper = _guestHomePageHelper;
    if (helper == null || !helper.hasSliderMovies) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        Space.height(16),
        GestureDetector(
          onPanDown: (_) => _stopAutoScroll(),
          onPanEnd: (_) => _restartAutoScroll(),
          child: SizedBox(
            height: MySize.height(220),
            child: PageView.builder(
              itemCount: helper.sliderMovies.length,
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentSliderPage = index;
                });
              },
              itemBuilder: (context, index) {
                final movie = helper.sliderMovies[index];
                return BannerMovieCard(
                  movie: movie,
                  onTap: () => helper.playMovie(movie, context),
                );
              },
            ),
          ),
        ),
        Space.height(12),
        _buildSliderDotIndicators(),
        Space.height(24),
      ],
    );
  }

  Widget _buildSliderDotIndicators() {
    final helper = _guestHomePageHelper;
    if (helper == null || !helper.hasSliderMovies) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        helper.sliderMovies.length,
        (index) => Container(
          width: MySize.width(8),
          height: MySize.height(8),
          margin: EdgeInsets.symmetric(horizontal: MySize.width(4)),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _currentSliderPage == index
                ? AppColorConstants.primaryColor
                : AppColorConstants.textHint,
          ),
        ),
      ),
    );
  }

  Widget _buildGridSection() {
    final helper = _guestHomePageHelper;
    if (helper == null || helper.gridMovies.isEmpty) {
      return _buildEmptyState();
    }

    // Calculate total items including ads (add ad every 6 items)
    final adInterval = 6;
    final totalAds = (helper.gridMovies.length / adInterval).floor();
    final totalItems = helper.gridMovies.length + totalAds;

    return GridView.builder(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.width(16),
        vertical: MySize.height(30),
      ),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.7,
        crossAxisSpacing: MySize.width(20),
        mainAxisSpacing: MySize.height(20),
      ),
      itemCount: totalItems,
      itemBuilder: (context, index) {
        // Check if this position should be an ad
        final adPositions = <int>[];
        for (int i = 1; i <= totalAds; i++) {
          adPositions.add((i * adInterval) + (i - 1));
        }

        if (adPositions.contains(index)) {
          // Show ad
          final adIndex = adPositions.indexOf(index);
          final bannerAd = AdsManager.getBannerAdWidget(
            index: adIndex % 10,
            uniqueId: 'guest_grid_ad_$adIndex',
          );
          return Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(MySize.radius(12)),
              border: Border.all(
                color: AppColorConstants.primaryColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(MySize.radius(12)),
              child: bannerAd ?? AdsManager.buildAdPlaceholder(),
            ),
          );
        }

        // Calculate movie index (accounting for ads before this position)
        final adsBeforeThisPosition = adPositions.where((pos) => pos < index).length;
        final movieIndex = index - adsBeforeThisPosition;

        if (movieIndex >= helper.gridMovies.length) {
          return const SizedBox.shrink();
        }

        final movie = helper.gridMovies[movieIndex];
        return MovieCard(
          margin: EdgeInsets.zero,
          movie: movie,
          onTap: () => helper.playMovie(movie, context),
          showTitle: true,
          showRating: true,
          showDuration: true,
        );
      },
    );
  }

  Widget _buildLoadMoreIndicator() {
    return Container(
      padding: EdgeInsets.all(MySize.height(16)),
      child: Center(
        child: CircularProgressIndicator(color: AppColorConstants.primaryColor),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: EdgeInsets.all(MySize.height(32)),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.movie_outlined,
              size: MySize.height(64),
              color: AppColorConstants.textHint,
            ),
            Space.height(16),
            AppText(
              text: 'No movies available',
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: AppColorConstants.textSecondary,
            ),
            Space.height(8),
            AppText(
              text: 'Please check your internet connection and try again',
              fontSize: 14,
              color: AppColorConstants.textHint,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showFilterBottomSheet() {
    showPreferenceSelectionBottomSheet(
      context,
      title: 'Filter Movies',
      onFiltersSelected: (selectedFilters) {
        // Refresh the page with new filters
        _guestHomePageHelper?.refreshWithFilters(selectedFilters);
      },
    );
  }

  void gotoLoginPage() {
    gotoGoogleLoginPage();
  }
}
