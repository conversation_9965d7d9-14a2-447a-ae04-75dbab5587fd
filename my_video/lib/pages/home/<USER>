import 'package:my_video/app_imports.dart';

class HomePageHelper {
  final HomePageState _state;
  final Logger _logger = Logger();

  // Loading state
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // Data variables
  List<MovieModel> _featuredMovies = [];
  List<CategoryWiseData> _categoryWiseData = [];
  Map<String, List<MovieModel>> _moviesByCategory = {};

  // Getters
  List<MovieModel> get featuredMovies => _featuredMovies;
  List<CategoryWiseData> get categories => _categoryWiseData;

  // API Status
  ApiStatus apiStatus = ApiStatus.initial;
  bool _hasLoadedData = false;

  HomePageHelper(this._state) {
    // Only load data if not already loaded (prevents reload on navigation back)
    if (!_hasLoadedData) {
      // Add a small delay to ensure authentication state is properly set
      // after login before making API calls
      Future.delayed(const Duration(milliseconds: 100), () {
        if (!_hasLoadedData) {
          loadInitialData();
        }
      });
    }
  }

  Future<void> loadInitialData() async {
    _setLoading(true);
    apiStatus = ApiStatus.loading;
    _state.homeController.update();

    try {
      // Check if offline and has cached data
      final offlineManager = OfflineManager.instance;
      if (!offlineManager.isOnline && offlineManager.hasCachedData()) {
        await _loadCachedData();
        _setLoading(false);
        _state.homeController.update();
        return;
      }

      // Ensure user is still logged in before making authenticated API calls
      if (!AuthHelper.isLoggedIn) {
        _logger.w(
          'User is no longer logged in, cannot load authenticated data',
        );
        apiStatus = ApiStatus.error;
        return;
      }

      // Ensure authentication token is available first, then load data
      _logger.i('Ensuring auth token before loading data...');
      await _ensureAuthToken();
      await loadCategoryWiseData();

      apiStatus = ApiStatus.success;
      _hasLoadedData = true;
      _logger.i('Successfully loaded data from showcatwise API');
    } catch (e) {
      _logger.e('Error loading initial data: $e');
      ErrorHandler.handleError(e, context: 'Home Data Loading');
      apiStatus = ApiStatus.error;

      // Try to load cached data as fallback
      await _loadCachedData();
    } finally {
      _setLoading(false);
      _state.homeController.update();
    }
  }

  Future<void> _ensureAuthToken() async {
    try {
      // For authenticated users, we should already have a user token
      final userToken = AppSharedPreference.getUserToken();
      if (userToken != null && userToken.isNotEmpty) {
        _logger.i('User authentication token available');
        return;
      }

      // If no user token but we're in authenticated mode, something is wrong
      if (AuthHelper.isLoggedIn) {
        _logger.w('User appears logged in but no token found');
        return;
      }

      // Fallback: Get static token (shouldn't happen in authenticated mode)
      final staticToken = AppSharedPreference.getString('static_token');
      if (staticToken != null && staticToken.isNotEmpty) {
        _logger.i('Static token already available');
        return;
      }

      // Get unregistered user token as last resort
      _logger.i('Getting unregistered user token...');
      final response = await RestHelper.post('/unregisteredusertoken');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['status'] == 1) {
          await AppSharedPreference.setString('static_token', data['token']);
          _logger.i('Unregistered token obtained successfully');
        } else {
          _logger.w('Failed to get unregistered token: ${data['message']}');
        }
      } else {
        _logger.w('Failed to get unregistered token: ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('Error getting authentication token: $e');
    }
  }

  Future<void> loadCategoryWiseData({String? isMovie}) async {
    const maxRetries = 3;
    const retryDelay = Duration(milliseconds: 500);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        _logger.i(
          'Loading category wise data for ${isMovie == "1" ? 'movies' : 'web series'} (attempt $attempt/$maxRetries)',
        );

        // Get user preferences for filtering
        final languageFilter = await UserPreferenceService.instance
            .getLanguageNamesForAPI();
        final genreFilter = await UserPreferenceService.instance
            .getGenreNamesForAPI();
        final categoryFilter = await UserPreferenceService.instance
            .getCategoryNamesForAPI();

        final response = await _state.homeController
            .getCategoryWiseMoviesFromAPI(
              languages: languageFilter,
              genres: genreFilter,
              categories: categoryFilter,
              isMovie: isMovie,
            );

        if (response.success && response.result.isNotEmpty) {
          _categoryWiseData = response.result;

          // Extract featured movies from the first category or create from all categories
          _featuredMovies = [];
          for (final categoryData in _categoryWiseData) {
            if (_featuredMovies.length < 5) {
              _featuredMovies.addAll(
                categoryData.data.take(5 - _featuredMovies.length),
              );
            }
          }

          // Populate movies by category map
          _moviesByCategory.clear();
          for (final categoryData in _categoryWiseData) {
            _moviesByCategory[categoryData.name] = categoryData.data;
          }

          _logger.i(
            'Loaded category wise data: ${_categoryWiseData.length} categories, ${_featuredMovies.length} featured movies',
          );
          _state.homeController.update();
          return; // Success, exit retry loop
        } else {
          _logger.w('No data received from showcatwise API');
          return; // No data but no error, exit retry loop
        }
      } catch (e) {
        _logger.e(
          'Error loading category wise data (attempt $attempt/$maxRetries): $e',
        );

        // If this is the last attempt, rethrow the error
        if (attempt == maxRetries) {
          rethrow;
        }

        // Wait before retrying (only if not the last attempt)
        if (attempt < maxRetries) {
          _logger.i('Retrying in ${retryDelay.inMilliseconds}ms...');
          await Future.delayed(retryDelay);
        }
      }
    }
  }

  Future<void> _loadCachedData() async {
    try {
      final offlineManager = OfflineManager.instance;
      _featuredMovies = offlineManager.getCachedFeaturedMovies();

      // For now, create empty category wise data from cached data
      _categoryWiseData = [];
      _moviesByCategory.clear();

      _logger.i(
        'Loaded cached data: ${_featuredMovies.length} featured movies',
      );
      apiStatus = ApiStatus.success;
      _hasLoadedData = true;
    } catch (e) {
      _logger.e('Error loading cached data: $e');
      apiStatus = ApiStatus.error;
    }
  }

  List<MovieModel> getMoviesByCategory(String categoryName) {
    return _moviesByCategory[categoryName] ?? [];
  }

  Future<void> refreshData() async {
    try {
      _logger.i('Refreshing home page data with current tab state');

      // Get current content type from tab controller
      String? isMovie;
      try {
        final contentTabController = Get.find<ContentTabController>();
        isMovie = contentTabController.currentApiValue;
        _logger.i(
          'Current tab: ${contentTabController.currentDisplayName} ($isMovie)',
        );
      } catch (e) {
        // Tab controller not found, default to movies
        isMovie = MovieContentType.movies.apiValue;
        _logger.w('ContentTabController not found, defaulting to movies');
      }

      // Clear existing data
      _categoryWiseData.clear();
      _featuredMovies.clear();
      _moviesByCategory.clear();

      // Set loading state
      _setLoading(true);
      apiStatus = ApiStatus.loading;
      _state.homeController.update();

      // Ensure user is still logged in before making authenticated API calls
      if (!AuthHelper.isLoggedIn) {
        _logger.w(
          'User is no longer logged in, cannot refresh authenticated data',
        );
        apiStatus = ApiStatus.error;
        return;
      }

      // Ensure authentication token is available first, then load data
      await _ensureAuthToken();
      await loadCategoryWiseData(isMovie: isMovie);

      apiStatus = ApiStatus.success;
      _hasLoadedData = true;
      _logger.i('Successfully refreshed home page data');
    } catch (e) {
      _logger.e('Error refreshing home page data: $e');
      ErrorHandler.handleError(e, context: 'Home Data Refresh');
      apiStatus = ApiStatus.error;

      // Try to load cached data as fallback
      await _loadCachedData();
    } finally {
      _setLoading(false);
      _state.homeController.update();
    }
  }

  // Method to handle authentication state changes
  Future<void> onAuthenticationStateChanged() async {
    try {
      _logger.i('Authentication state changed, refreshing home page data');
      _hasLoadedData = false;
      await loadInitialData();
    } catch (e) {
      _logger.e('Error handling authentication state change: $e');
    }
  }

  void playMovie(MovieModel movie, BuildContext context) {
    _logger.i('Playing movie: ${movie.title}');

    AdsManager.showInterstitialAd(
      onAdClosed: () {
        context.push(AppRoutes.moviePlayer, extra: movie);
      },
    );
  }

  void viewAllMovies(CategoryWiseData category) {
    _logger.i('View all movies for category: ${category.name}');
    AppHelper.showToast('View all ${category.name} movies');
  }

  // Load data for specific content type (Movies or Web Series)
  Future<void> loadDataForContentType(String isMovie) async {
    try {
      AppHelper.logDebug('Loading data for content type: $isMovie');

      // Clear existing data
      _categoryWiseData.clear();
      _featuredMovies.clear();
      _moviesByCategory.clear();

      // Set loading state
      _setLoading(true);

      // Ensure user is still logged in before making authenticated API calls
      if (!AuthHelper.isLoggedIn) {
        AppHelper.logDebug(
          'User is no longer logged in, cannot load content type data',
        );
        return;
      }

      // Ensure authentication token is available first, then load data with specific content type
      await _ensureAuthToken();
      await loadCategoryWiseData(isMovie: isMovie);

      AppHelper.logDebug('Successfully loaded data for content type: $isMovie');
    } catch (e) {
      AppHelper.logDebug('Error loading data for content type: $e');
      ErrorHandler.handleError(e);
    } finally {
      _setLoading(false);
    }
  }

  // Refresh data with new filters
  Future<void> refreshWithFilters(SelectedFilters filters) async {
    try {
      AppHelper.logDebug(
        'Refreshing logged-in home page with filters: $filters',
      );

      // Save filters to preferences
      await UserPreferenceService.instance.saveSelectedFilters(filters);

      // Clear existing data
      _categoryWiseData.clear();
      _featuredMovies.clear();
      _moviesByCategory.clear();

      // Ensure user is still logged in before making authenticated API calls
      if (!AuthHelper.isLoggedIn) {
        AppHelper.logDebug(
          'User is no longer logged in, cannot refresh with filters',
        );
        return;
      }

      // Reload data with new filters and current content type
      final contentTabController = Get.find<ContentTabController>();
      final isMovie = contentTabController.currentApiValue;
      await _ensureAuthToken();
      await loadCategoryWiseData(isMovie: isMovie);

      AppHelper.logDebug(
        'Successfully refreshed logged-in home page with filters',
      );
    } catch (e) {
      AppHelper.logDebug('Error refreshing with filters: $e');
      ErrorHandler.handleError(e);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    _state.homeController.update();
  }
}
