import 'package:my_video/app_imports.dart';

class GoogleLoginPage extends StatefulWidget {
  const GoogleLoginPage({super.key});

  @override
  State<GoogleLoginPage> createState() => _GoogleLoginPageState();
}

class _GoogleLoginPageState extends State<GoogleLoginPage> {
  GoogleLoginPageHelper? _googleLoginPageHelper;
  late AuthenticationController authController;

  @override
  Widget build(BuildContext context) {
    MySize.init(context);
    _googleLoginPageHelper =
        _googleLoginPageHelper ?? GoogleLoginPageHelper(() => setState(() {}));

    return GetBuilder(
      init: AuthenticationController(),
      builder: (AuthenticationController controller) {
        authController = controller;
        return AppScaffold(
          backgroundColor: AppColorConstants.colorBlack,
          body: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(MySize.width(24)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Back button
                  Align(
                    alignment: Alignment.topLeft,
                    child: IconButton(
                      onPressed: () => gotoBack(),
                      icon: Icon(
                        Icons.arrow_back_ios,
                        color: AppColorConstants.colorWhite,
                        size: MySize.width(24),
                      ),
                    ),
                  ),

                  SizedBox(height: MySize.height(40)),

                  // App Logo
                  Center(
                    child: Container(
                      width: MySize.width(120),
                      height: MySize.height(120),
                      decoration: BoxDecoration(
                        color: AppColorConstants.primaryColor,
                        borderRadius: BorderRadius.circular(MySize.width(20)),
                        boxShadow: [
                          BoxShadow(
                            color: AppColorConstants.primaryColor.withOpacity(
                              0.3,
                            ),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.play_circle_filled,
                        size: MySize.width(60),
                        color: AppColorConstants.colorWhite,
                      ),
                    ),
                  ),

                  SizedBox(height: MySize.height(40)),

                  // Welcome Text
                  AppText(
                    text: 'Welcome to MyVideo',
                    fontSize: MySize.size28,
                    fontWeight: FontWeight.bold,
                    color: AppColorConstants.colorWhite,
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: MySize.height(12)),

                  AppText(
                    text:
                        'Sign in to access your personalized movie experience',
                    fontSize: MySize.size16,
                    color: AppColorConstants.textSecondary,
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: MySize.height(60)),

                  // First Name Field
                  AppTextFormField(
                    controller: _googleLoginPageHelper!.firstNameController,
                    labelText: 'First Name',
                    hintText: 'Enter your first name',
                    textInputAction: TextInputAction.next,
                    prefixIcon: const Icon(Icons.person_outline),
                    validator: _googleLoginPageHelper!.validateFirstName,
                  ),

                  SizedBox(height: MySize.height(20)),

                  // Last Name Field
                  AppTextFormField(
                    controller: _googleLoginPageHelper!.lastNameController,
                    labelText: 'Last Name',
                    hintText: 'Enter your last name',
                    textInputAction: TextInputAction.done,
                    prefixIcon: const Icon(Icons.person_outline),
                    validator: _googleLoginPageHelper!.validateLastName,
                  ),

                  SizedBox(height: MySize.height(40)),

                  // Google Sign-In Button
                  _googleLoginPageHelper!.isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : Container(
                          height: MySize.height(56),
                          decoration: BoxDecoration(
                            color: AppColorConstants.colorWhite,
                            borderRadius: BorderRadius.circular(
                              MySize.width(12),
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: AppColorConstants.colorBlack.withOpacity(
                                  0.1,
                                ),
                                blurRadius: 10,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: _googleLoginPageHelper!.signInWithGoogle,
                              borderRadius: BorderRadius.circular(
                                MySize.width(12),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Image.asset(
                                    'assets/icons/google_icon.png',
                                    width: MySize.width(24),
                                    height: MySize.height(24),
                                    errorBuilder: (context, error, stackTrace) {
                                      return Icon(
                                        Icons.login,
                                        size: MySize.width(24),
                                        color: AppColorConstants.primaryColor,
                                      );
                                    },
                                  ),
                                  SizedBox(width: MySize.width(12)),
                                  AppText(
                                    text: 'Continue with Google',
                                    fontSize: MySize.size16,
                                    fontWeight: FontWeight.w600,
                                    color: AppColorConstants.colorBlack,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),

                  SizedBox(height: MySize.height(30)),

                  // Guest Access Button
                  TextButton(
                    onPressed: _googleLoginPageHelper!.continueAsGuest,
                    child: AppText(
                      text: 'Continue as Guest',
                      fontSize: MySize.size16,
                      color: AppColorConstants.textSecondary,
                      textDecoration: TextDecoration.underline,
                    ),
                  ),

                  SizedBox(height: MySize.height(40)),

                  // Terms and Privacy
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: MySize.width(20)),
                    child: AppText(
                      text:
                          'By continuing, you agree to our Terms of Service and Privacy Policy',
                      fontSize: MySize.size12,
                      color: AppColorConstants.textSecondary,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
