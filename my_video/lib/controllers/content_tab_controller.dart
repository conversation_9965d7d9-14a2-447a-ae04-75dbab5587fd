import 'package:my_video/app_imports.dart';



class ContentTabController extends GetxController
    with GetSingleTickerProviderStateMixin {
  late TabController tabController;
  final Logger _logger = Logger();

  // Current selected content type
  final Rx<MovieContentType> _currentContentType = MovieContentType.movies.obs;
  MovieContentType get currentContentType => _currentContentType.value;

  // Tab index
  int get currentTabIndex => _currentContentType.value.index;

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(
      length: MovieContentType.values.length,
      vsync: this,
      initialIndex: 0,
    );

    // Listen to tab changes
    tabController.addListener(_onTabChanged);

    _logger.i(
      'ContentTabController initialized with ${MovieContentType.values.length} tabs',
    );
  }

  @override
  void onClose() {
    tabController.removeListener(_onTabChanged);
    tabController.dispose();
    super.onClose();
  }

  void _onTabChanged() {
    if (!tabController.indexIsChanging) {
      final newContentType = MovieContentType.values[tabController.index];
      if (newContentType != _currentContentType.value) {
        _currentContentType.value = newContentType;
        _logger.i(
          'Tab changed to: ${newContentType.displayName} (${newContentType.apiValue})',
        );
        update();
      }
    }
  }

  // Programmatically change tab
  void changeTab(MovieContentType contentType) {
    final index = contentType.index;
    if (index != tabController.index) {
      tabController.animateTo(index);
      _currentContentType.value = contentType;
      _logger.i('Programmatically changed tab to: ${contentType.displayName}');
      update();
    }
  }

  // Get API value for current content type
  String get currentApiValue => _currentContentType.value.apiValue;

  // Get display name for current content type
  String get currentDisplayName => _currentContentType.value.displayName;

  // Check if current tab is movies
  bool get isMoviesTab => _currentContentType.value == MovieContentType.movies;

  // Check if current tab is web series
  bool get isWebSeriesTab =>
      _currentContentType.value == MovieContentType.webSeries;

  // Get all content types for building tabs
  List<MovieContentType> get allContentTypes => MovieContentType.values;

  // Reset to movies tab
  void resetToMovies() {
    changeTab(MovieContentType.movies);
  }

  // Toggle between movies and web series
  void toggleContentType() {
    final newType = isMoviesTab
        ? MovieContentType.webSeries
        : MovieContentType.movies;
    changeTab(newType);
  }
}
